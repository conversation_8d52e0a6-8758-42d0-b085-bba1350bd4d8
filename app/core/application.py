"""
Основной модуль приложения дашборда
Координирует работу всех компонентов и управляет состоянием
"""
import streamlit as st
import pandas as pd
import logging
from typing import Dict, Any, Optional
from datetime import datetime, date

# Импорты конфигурации
try:
    from config.app_config import app_config
    DASHBOARD_CONFIG = {'refresh_interval': 300}
except ImportError:
    DASHBOARD_CONFIG = {'refresh_interval': 300}

# Импорты данных
from data.loader import data_loader
from data.database import db_connection

# Импорты компонентов
from app.components.header import HeaderComponent
from app.components.sidebar import SidebarComponent
from app.components.top_filters import TopFiltersComponent
from app.components.metrics import MetricsComponent
from app.components.filters import FiltersComponent
from app.components.insights import InsightsComponent
from app.components.overview import OverviewComponent
from app.components.enhanced_dashboard import EnhancedDashboardComponents
from app.components.interactive_features import InteractiveFeatures
from app.components.analytics_module import AdvancedAnalytics

# Импорты стилей
from app.styles.integration import style_integrator, show_alert, show_empty_state
from app.styles.enhanced_styles import EnhancedStyleManager

logger = logging.getLogger(__name__)


class DashboardApplication:
    """Основной класс приложения дашборда"""
    
    def __init__(self):
        """Инициализирует приложение"""
        self.initialized = False
        self.components = {}
        self.current_filters = {}
        self.cached_data = {}
        self.style_manager = EnhancedStyleManager()
        
        try:
            self._initialize_components()
            self._load_initial_data()
            self.initialized = True
            logger.info("✅ Приложение инициализировано успешно")
            
        except Exception as e:
            logger.error(f"❌ Ошибка инициализации приложения: {e}")
            raise
    
    def _initialize_components(self) -> None:
        """Инициализирует все компоненты"""
        try:
            self.components = {
                'header': HeaderComponent(),
                'sidebar': SidebarComponent(),
                'top_filters': TopFiltersComponent(),
                'metrics': MetricsComponent(),
                'filters': FiltersComponent(),
                'insights': InsightsComponent(),
                'overview': OverviewComponent(),
                'enhanced_dashboard': EnhancedDashboardComponents(),
                'interactive_features': InteractiveFeatures(),
                'analytics': AdvancedAnalytics()
            }
            logger.debug("Компоненты инициализированы")
            
        except Exception as e:
            logger.error(f"❌ Ошибка инициализации компонентов: {e}")
            raise
    
    def _load_initial_data(self) -> None:
        """Загружает начальные данные"""
        try:
            # Загружаем сводную информацию
            self.cached_data['summary'] = data_loader.load_summary_data()
            
            # Загружаем данные о договорах
            self.cached_data['contracts'] = data_loader.load_contracts_overview()
            
            # Загружаем справочные данные
            self.cached_data['references'] = {
                'statuses': data_loader.get_contract_statuses(),
                'types': data_loader.get_contract_types(),
                'departments': data_loader.get_departments()
            }
            
            logger.debug("Начальные данные загружены")
            
        except Exception as e:
            logger.error(f"❌ Ошибка загрузки данных: {e}")
            # Не прерываем инициализацию, используем пустые данные
            self.cached_data = {
                'summary': {},
                'contracts': pd.DataFrame(),
                'references': {'statuses': [], 'types': [], 'departments': []}
            }
    
    def run(self) -> None:
        """Запускает основной цикл приложения"""
        if not self.initialized:
            show_alert("Приложение не инициализировано", "danger", "Ошибка")
            return
        
        try:
            # 0. Применяем улучшенные стили
            self.style_manager.inject_enhanced_styles()

            # 1. Отрисовываем заголовок
            self._render_header()
            
            # 2. Отрисовываем боковую панель и получаем фильтры
            filters = self._render_sidebar()
            
            # 3. Применяем фильтры если они изменились
            if filters != self.current_filters:
                self.current_filters = filters
                self._apply_filters(filters)
            
            # 4. Отрисовываем основной контент
            self._render_main_content()
            
        except Exception as e:
            logger.error(f"❌ Ошибка в основном цикле приложения: {e}")
            show_alert(f"Произошла ошибка: {e}", "danger", "Ошибка приложения")
    
    def _render_header(self) -> None:
        """Отрисовывает заголовок приложения"""
        try:
            self.components['header'].render()

        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки заголовка: {e}")
            # Fallback заголовок используя нативные компоненты
            st.title("📊 Дашборд освоения договоров")
            st.caption("Система мониторинга и анализа")
            st.error(f"Ошибка отображения заголовка: {e}")
    
    def _render_sidebar(self) -> Dict[str, Any]:
        """Отрисовывает боковую панель и возвращает фильтры"""
        try:
            return self.components['sidebar'].render(
                references=self.cached_data.get('references', {})
            )
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки боковой панели: {e}")
            return {}
    
    def _apply_filters(self, filters: Dict[str, Any]) -> None:
        """Применяет фильтры к данным"""
        try:
            if not filters:
                return
            
            # Применяем фильтры к данным о договорах
            contracts_df = self.cached_data.get('contracts', pd.DataFrame())
            
            if not contracts_df.empty:
                original_count = len(contracts_df)
                filtered_df = self._filter_dataframe(contracts_df, filters)
                filtered_count = len(filtered_df)

                # Сохраняем отфильтрованные данные
                self.cached_data['filtered_contracts'] = filtered_df
                logger.info(f"🔍 ОТЛАДКА: Сохранили filtered_contracts размером {len(filtered_df)}")

                # Пересчитываем метрики для отфильтрованных данных
                filtered_summary = self._calculate_summary(filtered_df)
                self.cached_data['filtered_summary'] = filtered_summary
                logger.info(f"🔍 ОТЛАДКА: Сохранили filtered_summary с ключами: {list(filtered_summary.keys()) if filtered_summary else 'None'}")

                logger.info(f"✅ Фильтры применены: {original_count} → {filtered_count} договоров")
                logger.debug(f"Активные фильтры: {filters}")

                # Проверяем, что данные действительно сохранились
                check_filtered = self.cached_data.get('filtered_contracts')
                if isinstance(check_filtered, pd.DataFrame):
                    logger.info(f"🔍 ОТЛАДКА: Проверка сохранения - filtered_contracts содержит {len(check_filtered)} записей")
                else:
                    logger.error("❌ ОТЛАДКА: Ошибка сохранения - filtered_contracts не является DataFrame!")
            else:
                logger.warning("⚠️ Нет данных для применения фильтров")
            
        except Exception as e:
            logger.error(f"❌ Ошибка применения фильтров: {e}")
    
    def _filter_dataframe(self, df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """Применяет фильтры к DataFrame с поддержкой всех новых фильтров"""
        filtered_df = df.copy()

        try:
            # Отладочная информация
            logger.info(f"🔍 Исходные данные: {len(filtered_df)} записей")
            if not filtered_df.empty:
                logger.info(f"🔍 Уникальные статусы в данных: {list(filtered_df['status'].unique())}")
                logger.info(f"🔍 Уникальные типы в данных: {list(filtered_df['contract_type'].unique())}")

            # Фильтр по статусу (поддержка множественного выбора)
            if filters.get('status'):
                logger.info(f"🔍 Применяем фильтр по статусу: {filters['status']}")
                if isinstance(filters['status'], list) and filters['status']:
                    filtered_df = filtered_df[filtered_df['status'].isin(filters['status'])]
                    logger.info(f"🔍 После фильтра по статусу (список): {len(filtered_df)} записей")
                elif isinstance(filters['status'], str) and filters['status'] != 'Все':
                    filtered_df = filtered_df[filtered_df['status'] == filters['status']]
                    logger.info(f"🔍 После фильтра по статусу (строка): {len(filtered_df)} записей")

            # Фильтр по типу договора (поддержка множественного выбора)
            if filters.get('contract_type'):
                logger.info(f"🔍 Применяем фильтр по типу договора: {filters['contract_type']}")
                if isinstance(filters['contract_type'], list) and filters['contract_type']:
                    filtered_df = filtered_df[filtered_df['contract_type'].isin(filters['contract_type'])]
                    logger.info(f"🔍 После фильтра по типу (список): {len(filtered_df)} записей")
                elif isinstance(filters['contract_type'], str) and filters['contract_type'] != 'Все':
                    filtered_df = filtered_df[filtered_df['contract_type'] == filters['contract_type']]
                    logger.info(f"🔍 После фильтра по типу (строка): {len(filtered_df)} записей")

            # Фильтр по департаменту (пропускаем, так как колонка department отсутствует в схеме)
            if filters.get('department') and filters['department'] != 'Все':
                logger.warning("Фильтр по департаменту пропущен - колонка department отсутствует в схеме БД")
                pass

            # Фильтр по дате начала (только если дата выбрана и не None)
            if filters.get('start_date') is not None:
                logger.info(f"🔍 Применяем фильтр по дате начала: {filters['start_date']}")

                # ОТЛАДКА: Показываем реальные даты в данных
                if not filtered_df.empty:
                    sample_dates = filtered_df['start_date'].head(5).tolist()
                    logger.info(f"🔍 ОТЛАДКА: Примеры дат в данных: {sample_dates}")
                    min_date = pd.to_datetime(filtered_df['start_date']).min()
                    max_date = pd.to_datetime(filtered_df['start_date']).max()
                    logger.info(f"🔍 ОТЛАДКА: Диапазон дат в данных: {min_date} - {max_date}")
                    logger.info(f"🔍 ОТЛАДКА: Фильтр требует дату >= {filters['start_date']}")

                try:
                    filtered_df = filtered_df[
                        pd.to_datetime(filtered_df['start_date']) >= pd.to_datetime(filters['start_date'])
                    ]
                    logger.info(f"🔍 После фильтра по дате начала: {len(filtered_df)} записей")
                except Exception as e:
                    logger.error(f"❌ Ошибка применения фильтра по дате начала: {e}")
            else:
                logger.info("🔍 Пропускаем фильтр по дате начала - дата не выбрана")

            # Фильтр по дате окончания (только если дата выбрана и не None)
            if filters.get('end_date') is not None:
                logger.info(f"🔍 Применяем фильтр по дате окончания: {filters['end_date']}")
                try:
                    filtered_df = filtered_df[
                        pd.to_datetime(filtered_df['end_date']) <= pd.to_datetime(filters['end_date'])
                    ]
                    logger.info(f"🔍 После фильтра по дате окончания: {len(filtered_df)} записей")
                except Exception as e:
                    logger.error(f"❌ Ошибка применения фильтра по дате окончания: {e}")
            else:
                logger.info("🔍 Пропускаем фильтр по дате окончания - дата не выбрана")

            # Фильтр по сумме (диапазон)
            if filters.get('amount_range'):
                min_amount, max_amount = filters['amount_range']
                logger.info(f"🔍 Применяем фильтр по сумме: {min_amount} - {max_amount}")
                if max_amount != float('inf'):
                    filtered_df = filtered_df[
                        (filtered_df['total_amount'] >= min_amount) &
                        (filtered_df['total_amount'] <= max_amount)
                    ]
                else:
                    filtered_df = filtered_df[filtered_df['total_amount'] >= min_amount]
                logger.info(f"🔍 После фильтра по сумме: {len(filtered_df)} записей")

            # Фильтр по поиску в названии
            if filters.get('search_query') and filters['search_query'].strip():
                search_query = filters['search_query'].strip().lower()
                search_mask = (
                    filtered_df['contract_name'].str.lower().str.contains(search_query, na=False) |
                    filtered_df['contract_number'].str.lower().str.contains(search_query, na=False)
                )
                filtered_df = filtered_df[search_mask]

            # Фильтр по подрядчику
            if filters.get('contractor_filter') and filters['contractor_filter'].strip():
                contractor_query = filters['contractor_filter'].strip().lower()
                if 'contractor_name' in filtered_df.columns:
                    contractor_mask = filtered_df['contractor_name'].str.lower().str.contains(contractor_query, na=False)
                    filtered_df = filtered_df[contractor_mask]

            # Фильтр по срокам (deadline_filter)
            if filters.get('deadline_filter') and filters['deadline_filter'] != 'Все':
                filtered_df = self._apply_deadline_filter(filtered_df, filters['deadline_filter'])

            # Фильтр по проценту выполнения
            if filters.get('completion_filter') and filters['completion_filter'] != 'Все':
                filtered_df = self._apply_completion_filter(filtered_df, filters['completion_filter'])

            # Применяем сортировку
            if filters.get('sort_filter'):
                filtered_df = self._apply_sorting(filtered_df, filters['sort_filter'])

            # Финальная отладочная информация
            logger.info(f"🔍 ИТОГОВЫЙ РЕЗУЛЬТАТ ФИЛЬТРАЦИИ: {len(filtered_df)} записей")

            return filtered_df

        except Exception as e:
            logger.error(f"❌ Ошибка фильтрации данных: {e}")
            return df

    def _apply_deadline_filter(self, df: pd.DataFrame, deadline_filter: str) -> pd.DataFrame:
        """Применяет фильтр по срокам"""
        try:
            today = pd.Timestamp.now().date()

            if deadline_filter == 'Просроченные':
                return df[pd.to_datetime(df['end_date']).dt.date < today]
            elif deadline_filter == 'Заканчиваются в течение месяца':
                month_later = today + pd.Timedelta(days=30)
                return df[
                    (pd.to_datetime(df['end_date']).dt.date >= today) &
                    (pd.to_datetime(df['end_date']).dt.date <= month_later)
                ]
            elif deadline_filter == 'Долгосрочные':
                year_later = today + pd.Timedelta(days=365)
                return df[pd.to_datetime(df['end_date']).dt.date > year_later]

            return df
        except Exception as e:
            logger.error(f"❌ Ошибка применения фильтра по срокам: {e}")
            return df

    def _apply_completion_filter(self, df: pd.DataFrame, completion_filter: str) -> pd.DataFrame:
        """Применяет фильтр по проценту выполнения"""
        try:
            if 'completion_percentage' not in df.columns:
                logger.warning("Колонка completion_percentage отсутствует, пропускаем фильтр")
                return df

            if completion_filter == 'Менее 25%':
                return df[df['completion_percentage'] < 25]
            elif completion_filter == '25-50%':
                return df[(df['completion_percentage'] >= 25) & (df['completion_percentage'] < 50)]
            elif completion_filter == '50-75%':
                return df[(df['completion_percentage'] >= 50) & (df['completion_percentage'] < 75)]
            elif completion_filter == 'Более 75%':
                return df[df['completion_percentage'] >= 75]
            elif completion_filter == '100%':
                return df[df['completion_percentage'] == 100]

            return df
        except Exception as e:
            logger.error(f"❌ Ошибка применения фильтра по проценту выполнения: {e}")
            return df

    def _apply_sorting(self, df: pd.DataFrame, sort_filter: str) -> pd.DataFrame:
        """Применяет сортировку к DataFrame"""
        try:
            if sort_filter == 'По дате создания':
                # Используем created_at если есть, иначе start_date
                if 'created_at' in df.columns:
                    return df.sort_values('created_at', ascending=False)
                else:
                    return df.sort_values('start_date', ascending=False)
            elif sort_filter == 'По сумме (убыв.)':
                return df.sort_values('total_amount', ascending=False)
            elif sort_filter == 'По названию':
                return df.sort_values('contract_name', ascending=True)
            elif sort_filter == 'По сроку окончания':
                return df.sort_values('end_date', ascending=True)

            return df
        except Exception as e:
            logger.error(f"❌ Ошибка применения сортировки: {e}")
            return df

    def _calculate_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Вычисляет сводную статистику для DataFrame"""
        if df.empty:
            return {}
        
        try:
            return {
                'total_contracts': len(df),
                'active_contracts': len(df[df['status'] == 'active']),
                'total_amount': df['total_amount'].sum(),
                'average_amount': df['total_amount'].mean(),
                'overdue_contracts': len(df[df['status'] == 'overdue']),
                'completed_contracts': len(df[df['status'] == 'completed']),
                'overdue_rate': (len(df[df['status'] == 'overdue']) / len(df)) * 100 if len(df) > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"❌ Ошибка вычисления статистики: {e}")
            return {}
    
    def _render_main_content(self) -> None:
        """Отрисовывает основной контент"""
        try:
            # Получаем данные для отображения - безопасно обрабатываем DataFrame
            summary_data = self.cached_data.get('filtered_summary')
            if summary_data is None:
                summary_data = self.cached_data.get('summary', {})

            contracts_data = self.cached_data.get('filtered_contracts')
            if contracts_data is None or (isinstance(contracts_data, pd.DataFrame) and contracts_data.empty):
                contracts_data = self.cached_data.get('contracts', pd.DataFrame())

            # Убеждаемся, что contracts_data это DataFrame
            if not isinstance(contracts_data, pd.DataFrame):
                contracts_data = pd.DataFrame()

            # Убеждаемся, что summary_data это словарь
            if not isinstance(summary_data, dict):
                summary_data = {}

            # Проверяем наличие данных - используем безопасные проверки
            has_summary_data = isinstance(summary_data, dict) and len(summary_data) > 0
            has_contracts_data = isinstance(contracts_data, pd.DataFrame) and not contracts_data.empty

            if not has_summary_data and not has_contracts_data:
                show_empty_state(
                    "Нет данных для отображения",
                    "Проверьте подключение к базе данных или настройки фильтров",
                    "📊"
                )
                return

            # 1. Отрисовываем улучшенные KPI карточки
            if isinstance(summary_data, dict) and len(summary_data) > 0:
                self.components['enhanced_dashboard'].render_enhanced_kpi_cards(summary_data)

                # Добавляем панель быстрых действий
                self.components['enhanced_dashboard'].render_quick_actions_panel()

                # Добавляем обновления в реальном времени
                self.components['enhanced_dashboard'].render_real_time_updates()

            # 2. Отрисовываем фильтры (если включены)
            if self.current_filters.get('show_advanced_filters', False):
                self.components['filters'].render(self.current_filters)

            # 3. Отрисовываем интерактивные графики
            if isinstance(contracts_data, pd.DataFrame) and not contracts_data.empty:
                self.components['enhanced_dashboard'].render_interactive_charts(contracts_data)

                # Отрисовываем умные инсайты
                self.components['enhanced_dashboard'].render_insights_panel(contracts_data)

                # Добавляем расширенную аналитику
                self.components['analytics'].render_analytics_dashboard(contracts_data)

            # 4. Добавляем интерактивные функции поиска и фильтрации
            if isinstance(contracts_data, pd.DataFrame) and not contracts_data.empty:
                # Применяем расширенный поиск и фильтрацию
                filtered_contracts = self.components['interactive_features'].render_advanced_search(contracts_data)

                # Добавляем опции экспорта данных
                self.components['interactive_features'].render_data_export_options(filtered_contracts)

                # Отрисовываем обзор с отфильтрованными данными
                self.components['overview'].render(filtered_contracts, self.current_filters)
            else:
                # Если нет данных, все равно показываем интерфейс поиска
                self.components['interactive_features'].render_advanced_search(pd.DataFrame())

        except Exception as e:
            import traceback
            logger.error(f"❌ Ошибка отрисовки основного контента: {e}")
            logger.error(f"Трассировка стека: {traceback.format_exc()}")
            show_alert(f"Ошибка отображения данных: {e}", "danger", "Ошибка")
    
    def refresh_data(self) -> None:
        """Обновляет данные из источников"""
        try:
            logger.info("🔄 Обновление данных...")
            
            # Очищаем кеш
            self.cached_data.clear()
            
            # Загружаем данные заново
            self._load_initial_data()
            
            # Применяем текущие фильтры
            if self.current_filters:
                self._apply_filters(self.current_filters)
            
            show_alert("Данные успешно обновлены", "success", "Обновление")
            logger.info("✅ Данные обновлены успешно")
            
        except Exception as e:
            logger.error(f"❌ Ошибка обновления данных: {e}")
            show_alert(f"Ошибка обновления данных: {e}", "danger", "Ошибка")
    
    def get_cached_data(self, key: str) -> Any:
        """Возвращает кешированные данные по ключу"""
        return self.cached_data.get(key)
    
    def set_cached_data(self, key: str, value: Any) -> None:
        """Устанавливает кешированные данные"""
        self.cached_data[key] = value
    
    def get_current_filters(self) -> Dict[str, Any]:
        """Возвращает текущие фильтры"""
        return self.current_filters.copy()
    
    def is_initialized(self) -> bool:
        """Проверяет, инициализировано ли приложение"""
        return self.initialized

    def run_with_top_filters(self) -> None:
        """Запускает приложение с централизованными фильтрами в верхней части"""
        if not self.initialized:
            show_alert("Приложение не инициализировано", "danger", "Ошибка")
            return

        try:
            # 0. Применяем улучшенные стили
            self.style_manager.inject_enhanced_styles()

            # 1. Отрисовываем заголовок
            self._render_header()

            # 2. Отрисовываем централизованные фильтры сразу после заголовка
            filters = self._render_top_filters()

            # 3. Всегда применяем фильтры для обеспечения реактивности
            # Сравниваем фильтры более надежным способом
            filters_changed = self._filters_changed(filters, self.current_filters)

            if filters_changed or not hasattr(self, '_last_filter_hash'):
                self.current_filters = filters.copy()
                self._apply_filters(filters)
                # Сохраняем хеш фильтров для отслеживания изменений
                self._last_filter_hash = self._get_filters_hash(filters)
                logger.debug(f"Фильтры применены: {filters}")

            # 4. Отрисовываем основной контент с отфильтрованными данными
            self._render_filtered_content()

            # 5. Закрываем контейнер с отступом для фиксированной панели
            st.markdown('</div>', unsafe_allow_html=True)

        except Exception as e:
            logger.error(f"❌ Ошибка в основном цикле приложения с верхними фильтрами: {e}")
            show_alert(f"Произошла ошибка: {e}", "danger", "Ошибка приложения")

    def _filters_changed(self, new_filters: Dict[str, Any], old_filters: Dict[str, Any]) -> bool:
        """Проверяет, изменились ли фильтры"""
        try:
            # Если старых фильтров нет, считаем что изменились
            if not old_filters:
                return True

            # Сравниваем ключи
            if set(new_filters.keys()) != set(old_filters.keys()):
                return True

            # Сравниваем значения
            for key in new_filters.keys():
                new_val = new_filters[key]
                old_val = old_filters.get(key)

                # Специальная обработка для списков
                if isinstance(new_val, list) and isinstance(old_val, list):
                    if sorted(new_val) != sorted(old_val):
                        return True
                elif new_val != old_val:
                    return True

            return False
        except Exception as e:
            logger.error(f"❌ Ошибка сравнения фильтров: {e}")
            return True  # В случае ошибки считаем что изменились

    def _get_filters_hash(self, filters: Dict[str, Any]) -> str:
        """Получает хеш фильтров для отслеживания изменений"""
        try:
            import hashlib
            import json

            # Преобразуем фильтры в строку для хеширования
            filters_str = json.dumps(filters, sort_keys=True, default=str)
            return hashlib.md5(filters_str.encode()).hexdigest()
        except Exception as e:
            logger.error(f"❌ Ошибка создания хеша фильтров: {e}")
            return str(hash(str(filters)))

    def _render_top_filters(self) -> Dict[str, Any]:
        """Отрисовывает централизованные фильтры в верхней части"""
        try:
            contracts_data = self.cached_data.get('contracts', pd.DataFrame())
            references = self.cached_data.get('references', {})

            return self.components['top_filters'].render(contracts_data, references)

        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки верхних фильтров: {e}")
            return {}

    def _render_filtered_content(self) -> None:
        """Отрисовывает основной контент с учетом фильтров"""
        try:
            # УБИРАЕМ повторное применение фильтров - это причина обнуления данных!
            # Фильтры уже применены в run_with_top_filters()

            # Получаем отфильтрованные данные с детальным логированием
            logger.info(f"🔍 ОТЛАДКА: Содержимое cached_data: {list(self.cached_data.keys())}")

            # Проверяем отфильтрованные данные
            filtered_contracts = self.cached_data.get('filtered_contracts')
            logger.info(f"🔍 ОТЛАДКА: filtered_contracts тип: {type(filtered_contracts)}")
            if isinstance(filtered_contracts, pd.DataFrame):
                logger.info(f"🔍 ОТЛАДКА: filtered_contracts размер: {len(filtered_contracts)}")

            filtered_summary = self.cached_data.get('filtered_summary')
            logger.info(f"🔍 ОТЛАДКА: filtered_summary тип: {type(filtered_summary)}")

            # Получаем отфильтрованные данные
            summary_data = self.cached_data.get('filtered_summary')
            if summary_data is None:
                logger.info("🔍 ОТЛАДКА: filtered_summary отсутствует, используем оригинальный summary")
                summary_data = self.cached_data.get('summary', {})

            contracts_data = self.cached_data.get('filtered_contracts')
            if contracts_data is None or (isinstance(contracts_data, pd.DataFrame) and contracts_data.empty):
                logger.info("🔍 ОТЛАДКА: filtered_contracts отсутствует или пуст, используем оригинальные contracts")
                contracts_data = self.cached_data.get('contracts', pd.DataFrame())

            logger.info(f"🔍 ОТЛАДКА: Итоговые данные для отрисовки - contracts: {len(contracts_data) if isinstance(contracts_data, pd.DataFrame) else 0} записей")

            # Убеждаемся в правильных типах данных
            if not isinstance(contracts_data, pd.DataFrame):
                contracts_data = pd.DataFrame()
            if not isinstance(summary_data, dict):
                summary_data = {}

            # Проверяем наличие данных с детальным логированием
            has_summary_data = isinstance(summary_data, dict) and len(summary_data) > 0
            has_contracts_data = isinstance(contracts_data, pd.DataFrame) and not contracts_data.empty

            logger.info(f"🔍 ОТЛАДКА: has_summary_data: {has_summary_data}")
            logger.info(f"🔍 ОТЛАДКА: has_contracts_data: {has_contracts_data}")

            if not has_summary_data and not has_contracts_data:
                logger.warning("⚠️ ОТЛАДКА: Нет данных для отображения - показываем пустое состояние")
                show_empty_state(
                    "Нет данных для отображения",
                    "Проверьте подключение к базе данных или настройки фильтров",
                    "📊"
                )
                return

            logger.info("✅ ОТЛАДКА: Данные найдены, начинаем отрисовку компонентов")

            # 1. Отрисовываем KPI карточки
            if has_summary_data:
                logger.info("🔍 ОТЛАДКА: Отрисовываем KPI карточки")
                self.components['enhanced_dashboard'].render_enhanced_kpi_cards(summary_data)
            else:
                logger.info("🔍 ОТЛАДКА: Пропускаем KPI карточки - нет summary_data")

            # 2. Отрисовываем графики (если включены в фильтрах)
            show_charts = self.current_filters.get('show_charts', True)
            logger.info(f"🔍 ОТЛАДКА: show_charts: {show_charts}, has_contracts_data: {has_contracts_data}")
            if show_charts and has_contracts_data:
                logger.info(f"🔍 ОТЛАДКА: Отрисовываем интерактивные графики с {len(contracts_data)} записями")
                self.components['enhanced_dashboard'].render_interactive_charts(contracts_data)
            else:
                logger.info("🔍 ОТЛАДКА: Пропускаем графики")

            # 3. Отрисовываем аналитику
            if has_contracts_data:
                logger.info(f"🔍 ОТЛАДКА: Отрисовываем аналитику с {len(contracts_data)} записями")
                self.components['analytics'].render_analytics_dashboard(contracts_data)
            else:
                logger.info("🔍 ОТЛАДКА: Пропускаем аналитику - нет данных")

            # 4. Отрисовываем обзор с отфильтрованными данными
            if has_contracts_data:
                logger.info(f"🔍 ОТЛАДКА: Отрисовываем обзор с {len(contracts_data)} записями")
                filtered_contracts = self.components['interactive_features'].render_advanced_search(contracts_data)
                logger.info(f"🔍 ОТЛАДКА: После advanced_search: {len(filtered_contracts) if isinstance(filtered_contracts, pd.DataFrame) else 0} записей")
                self.components['overview'].render(filtered_contracts, self.current_filters)
            else:
                logger.info("🔍 ОТЛАДКА: Пропускаем обзор - нет данных, отрисовываем пустой advanced_search")
                self.components['interactive_features'].render_advanced_search(pd.DataFrame())

        except Exception as e:
            import traceback
            logger.error(f"❌ Ошибка отрисовки отфильтрованного контента: {e}")
            logger.error(f"Трассировка стека: {traceback.format_exc()}")
            show_alert(f"Ошибка отображения данных: {e}", "danger", "Ошибка")
