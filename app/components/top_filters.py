"""
Компонент централизованных фильтров в верхней части дашборда
Объединяет все фильтры из sidebar и dashboard_components в единую панель
"""
import streamlit as st
import pandas as pd
import logging
from datetime import datetime, date, timedelta
from typing import Dict, Any, List, Optional


logger = logging.getLogger(__name__)


class TopFiltersComponent:
    """Компонент централизованных фильтров в верхней части дашборда"""
    
    def __init__(self):
        """Инициализирует компонент фильтров"""
        self.default_filters = {
            'status': 'Все',
            'contract_type': 'Все',
            'department': 'Все',
            'period_filter': 'Текущий год',
            'amount_filter': 'Все суммы',
            'search_query': '',
            'contractor_filter': '',
            'sort_filter': 'По дате создания',
            'show_charts': True,
            'start_date': None,
            'end_date': None,
            'amount_range': (0, 1000000000),
            'completion_filter': 'Все',
            'deadline_filter': 'Все',
            'page_size': 20
        }
    
    def render(self, contracts_df: pd.DataFrame, references: Dict[str, List] = None) -> Dict[str, Any]:
        """Отрисовывает централизованную панель фильтров"""
        try:
            # Стильный заголовок панели фильтров
            # st.markdown("""
            # <div style="
            #     background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            #     border: 2px solid #3b82f6;
            #     border-radius: 12px;
            #     padding: 1.5rem;
            #     margin-bottom: 2rem;
            #     box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            # ">
            #     <h3 style="
            #         color: #1e40af;
            #         font-size: 1.3rem;
            #         font-weight: 700;
            #         margin: 0 0 1rem 0;
            #         display: flex;
            #         align-items: center;
            #         gap: 0.5rem;
            #         text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            #     ">🔍 Фильтры и настройки дашборда</h3>
            # </div>
            # """, unsafe_allow_html=True)
            
            filters = {}
            
            # Первая строка фильтров - основные параметры
            st.markdown("### 📊 Основные параметры")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                # Получаем уникальные статусы из данных
                if not contracts_df.empty and 'status' in contracts_df.columns:
                    available_statuses = sorted(list(contracts_df['status'].unique()))
                    # Временное отладочное сообщение
                    # st.info(f"🔍 Статусы в БД: {available_statuses}")
                else:
                    available_statuses = ['active', 'completed', 'suspended', 'cancelled']
                    st.warning("⚠️ Нет данных о статусах, используем значения по умолчанию")

                # Множественный выбор статусов
                selected_statuses = st.multiselect(
                    "📊 Статус договора",
                    options=available_statuses,
                    default=available_statuses,  # По умолчанию выбраны все статусы
                    key="top_status_filter",
                    help="Выберите один или несколько статусов для фильтрации"
                )

                # Если ничего не выбрано, показываем все
                filters['status'] = selected_statuses if selected_statuses else available_statuses

                # Отладочная информация
                # st.info(f"🔍 Выбранные статусы: {filters['status']}")
            
            with col2:
                # Получаем уникальные типы договоров из данных
                if not contracts_df.empty and 'contract_type' in contracts_df.columns:
                    available_types = sorted(list(contracts_df['contract_type'].unique()))
                else:
                    available_types = ['construction', 'supply', 'services', 'maintenance']

                # Множественный выбор типов договоров
                selected_types = st.multiselect(
                    "🏗️ Тип договора",
                    options=available_types,
                    default=available_types,  # По умолчанию выбраны все типы
                    key="top_type_filter",
                    help="Выберите один или несколько типов договоров"
                )

                # Если ничего не выбрано, показываем все
                filters['contract_type'] = selected_types if selected_types else available_types
            
            with col3:
                # Фильтр по департаменту (если есть в данных)
                if references and 'departments' in references:
                    departments = ['Все'] + references['departments']
                else:
                    departments = ['Все', 'IT', 'Закупки', 'Финансы', 'Юридический']
                filters['department'] = st.selectbox(
                    "🏢 Департамент",
                    options=departments,
                    key="top_department_filter"
                )
            
            with col4:
                filters['period_filter'] = st.selectbox(
                    "📅 Период анализа",
                    options=['Текущий год', 'Последние 12 месяцев', 'Последние 6 месяцев', 'Текущий квартал'],
                    key="top_period_filter"
                )
            
            # Вторая строка - финансовые и временные параметры
            st.markdown("### 💰 Финансовые и временные параметры")
            col5, col6, col7, col8 = st.columns(4)
            
            with col5:
                filters['amount_filter'] = st.selectbox(
                    "💰 Диапазон сумм",
                    options=['Все суммы', 'До 10 млн', '10-50 млн', '50-100 млн', 'Свыше 100 млн'],
                    key="top_amount_filter"
                )
            
            with col6:
                filters['completion_filter'] = st.selectbox(
                    "📈 Процент выполнения",
                    options=['Все', 'Менее 25%', '25-50%', '50-75%', 'Более 75%', '100%'],
                    key="top_completion_filter"
                )
            
            with col7:
                filters['deadline_filter'] = st.selectbox(
                    "⏰ По срокам",
                    options=['Все', 'Просроченные', 'Заканчиваются в течение месяца', 'Долгосрочные'],
                    key="top_deadline_filter"
                )
            
            with col8:
                # Точный диапазон дат - используем чекбокс для включения фильтра
                enable_date_filter = st.checkbox("📅 Фильтр по дате", key="enable_date_filter", value=False)

                if enable_date_filter:
                    today = date.today()
                    start_of_year = date(today.year, 1, 1)
                    filters['start_date'] = st.date_input(
                        "📅 Дата начала",
                        value=start_of_year,
                        key="top_start_date_filter"
                    )
                else:
                    filters['start_date'] = None
            
            # Третья строка - поиск и настройки отображения
            st.markdown("### 🔍 Поиск и настройки отображения")
            col9, col10, col11, col12 = st.columns(4)
            
            with col9:
                filters['search_query'] = st.text_input(
                    "🔍 Поиск по названию",
                    placeholder="Введите название договора...",
                    key="top_search_filter"
                )
            
            with col10:
                filters['contractor_filter'] = st.text_input(
                    "🏢 Подрядчик",
                    placeholder="Название подрядчика...",
                    key="top_contractor_filter"
                )
            
            with col11:
                filters['sort_filter'] = st.selectbox(
                    "📈 Сортировка",
                    options=['По дате создания', 'По сумме (убыв.)', 'По названию', 'По сроку окончания'],
                    key="top_sort_filter"
                )
            
            with col12:
                filters['page_size'] = st.selectbox(
                    "📄 Записей на странице",
                    options=[10, 20, 50, 100],
                    index=1,
                    key="top_page_size_filter"
                )
            
            # Четвертая строка - дополнительные настройки
            st.markdown("### ⚙️ Дополнительные настройки")
            col13, col14, col15, col16 = st.columns(4)
            
            with col13:
                filters['show_charts'] = st.checkbox(
                    "📊 Показать графики",
                    value=True,
                    key="top_show_charts"
                )
            
            with col14:
                filters['auto_refresh'] = st.checkbox(
                    "🔄 Автообновление",
                    value=False,
                    key="top_auto_refresh"
                )
            
            with col15:
                # Кнопка сброса фильтров
                if st.button("🗑️ Сбросить фильтры", key="top_reset_filters"):
                    self._reset_filters()
                    st.rerun()
            
            with col16:
                # Кнопка обновления данных
                if st.button("🔄 Обновить данные", key="top_refresh_data"):
                    if 'dashboard_app' in st.session_state:
                        st.session_state.dashboard_app.refresh_data()
                        st.rerun()
            
            # Добавляем конечную дату если включен фильтр по дате
            if enable_date_filter and filters.get('start_date'):
                today = date.today()  # Определяем today здесь
                filters['end_date'] = st.date_input(
                    "📅 Дата окончания",
                    value=today,
                    min_value=filters['start_date'],
                    key="top_end_date_filter"
                )
            else:
                filters['end_date'] = None

            # Преобразуем диапазон сумм в числовые значения
            filters['amount_range'] = self._convert_amount_filter(filters['amount_filter'])

            # Показываем информацию о количестве записей
            if not contracts_df.empty:
                total_contracts = len(contracts_df)
                st.info(f"📊 Всего договоров в базе: **{total_contracts:,}**")



            logger.debug(f"Централизованные фильтры отрисованы, параметров: {len(filters)}")
            return filters
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки централизованных фильтров: {e}")
            return self.default_filters.copy()
    
    def _reset_filters(self) -> None:
        """Сбрасывает все фильтры к значениям по умолчанию"""
        try:
            # Очищаем все ключи фильтров из session_state
            filter_keys = [key for key in st.session_state.keys() if key.startswith('top_')]
            for key in filter_keys:
                del st.session_state[key]

            # Также сбрасываем чекбокс фильтра по дате
            if 'enable_date_filter' in st.session_state:
                del st.session_state['enable_date_filter']

            logger.info("✅ Фильтры сброшены")
        except Exception as e:
            logger.error(f"❌ Ошибка сброса фильтров: {e}")
    
    def _convert_amount_filter(self, amount_filter: str) -> tuple:
        """Преобразует текстовый фильтр суммы в числовые значения"""
        try:
            if amount_filter == 'До 10 млн':
                return (0, 10_000_000)
            elif amount_filter == '10-50 млн':
                return (10_000_000, 50_000_000)
            elif amount_filter == '50-100 млн':
                return (50_000_000, 100_000_000)
            elif amount_filter == 'Свыше 100 млн':
                return (100_000_000, float('inf'))
            else:  # 'Все суммы'
                return (0, float('inf'))
        except Exception as e:
            logger.error(f"❌ Ошибка преобразования фильтра суммы: {e}")
            return (0, float('inf'))
