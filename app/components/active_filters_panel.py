"""
Active Filters Panel Component
Displays currently active filters as chips/tags with reset functionality
"""
import streamlit as st
import json
import logging
from typing import Dict, Any, List
from datetime import date

logger = logging.getLogger(__name__)
import logging

logger = logging.getLogger(__name__)


class ActiveFiltersPanel:
    """Компонент для отображения активных фильтров"""
    
    def __init__(self):
        self.panel_key = "active_filters_panel"
    
    def render(self, filters: Dict[str, Any], available_statuses: List[str], available_types: List[str]) -> bool:
        """
        Отрисовывает панель активных фильтров
        
        Args:
            filters: Текущие фильтры
            available_statuses: Доступные статусы
            available_types: Доступные типы договоров
            
        Returns:
            bool: True если была нажата кнопка сброса фильтров
        """
        active_filter_tags = self._get_active_filter_tags(filters, available_statuses, available_types)

        logger.info(f"🔍 АКТИВНЫЕ ФИЛЬТРЫ: Получены теги: {len(active_filter_tags)} шт.")
        logger.info(f"🔍 АКТИВНЫЕ ФИЛЬТРЫ: Теги: {active_filter_tags}")

        # Показываем панель только если есть активные фильтры
        if not active_filter_tags:
            logger.info("🔍 АКТИВНЫЕ ФИЛЬТРЫ: Нет активных фильтров, панель скрыта")
            return False

        logger.info("🔍 АКТИВНЫЕ ФИЛЬТРЫ: Есть активные фильтры, отображаем панель")
        
        # CSS для полностью фиксированной панели в верхней части страницы
        st.markdown("""
        <style>
        /* Основные стили для фиксированной панели */
        .active-filters-panel {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
            z-index: 999999 !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-bottom: 2px solid #dee2e6 !important;
            padding: 12px 16px !important;
            margin: 0 !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            backdrop-filter: blur(15px) !important;
            -webkit-backdrop-filter: blur(15px) !important;
            display: block !important;
            visibility: visible !important;
        }

        /* Дополнительные селекторы для обеспечения фиксации */
        div[data-testid="stAppViewContainer"] .active-filters-panel {
            position: fixed !important;
            top: 0 !important;
            z-index: 999999 !important;
        }

        .stApp .active-filters-panel {
            position: fixed !important;
            top: 0 !important;
            z-index: 999999 !important;
        }

        /* Отступ для основного контента Streamlit */
        .main-content-with-fixed-panel {
            padding-top: 80px !important;
            margin-top: 0 !important;
        }

        /* Отступ для основного контейнера Streamlit */
        div[data-testid="stAppViewContainer"] > .main > .block-container {
            padding-top: 80px !important;
        }

        /* Дополнительные селекторы для основного контента */
        .stApp > div[data-testid="stAppViewContainer"] > .main {
            padding-top: 80px !important;
        }

        /* Убираем верхний отступ у первого элемента */
        .main-content-with-fixed-panel > *:first-child {
            margin-top: 0 !important;
        }

        /* Обеспечиваем, что панель всегда поверх Streamlit элементов */
        .active-filters-panel {
            z-index: 999999 !important;
            position: fixed !important;
            top: 0 !important;
        }

        /* Дополнительные стили для тегов в фиксированной панели */
        .active-filters-panel .filter-tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;
        }

        .active-filters-panel .active-filters-title {
            font-size: 0.9em;
            font-weight: 600;
            color: #495057;
            white-space: nowrap;
        }

        /* Стили для интерактивных тегов фильтров */
        .filter-tag {
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            position: relative !important;
        }

        .filter-tag:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
        }

        .filter-tag:active {
            transform: translateY(0) !important;
        }

        /* Стили для контекстного меню */
        .filter-context-menu {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 9999999 !important;
            background: rgba(0, 0, 0, 0.5) !important;
            width: 100vw !important;
            height: 100vh !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .context-menu-content {
            background: white !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
            min-width: 300px !important;
            max-width: 500px !important;
            max-height: 80vh !important;
            overflow: hidden !important;
            animation: contextMenuAppear 0.2s ease-out !important;
        }

        @keyframes contextMenuAppear {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .context-menu-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
            color: white !important;
            padding: 16px 20px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            font-weight: 600 !important;
        }

        .context-menu-close {
            background: none !important;
            border: none !important;
            color: white !important;
            font-size: 24px !important;
            cursor: pointer !important;
            padding: 0 !important;
            width: 30px !important;
            height: 30px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: background 0.2s ease !important;
        }

        .context-menu-close:hover {
            background: rgba(255,255,255,0.2) !important;
        }

        .context-menu-body {
            padding: 20px !important;
            max-height: 400px !important;
            overflow-y: auto !important;
        }

        .context-menu-footer {
            padding: 16px 20px !important;
            border-top: 1px solid #e9ecef !important;
            display: flex !important;
            gap: 12px !important;
            justify-content: flex-end !important;
        }

        .context-menu-apply, .context-menu-cancel {
            padding: 8px 16px !important;
            border: none !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
        }

        .context-menu-apply {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
            color: white !important;
        }

        .context-menu-apply:hover {
            background: linear-gradient(135deg, #1e7e34 0%, #155724 100%) !important;
            transform: translateY(-1px) !important;
        }

        .context-menu-cancel {
            background: #f8f9fa !important;
            color: #495057 !important;
            border: 1px solid #dee2e6 !important;
        }

        .context-menu-cancel:hover {
            background: #e9ecef !important;
        }

        /* Стили для содержимого контекстного меню */
        .filter-options h4 {
            margin: 0 0 16px 0 !important;
            color: #495057 !important;
            font-size: 16px !important;
            font-weight: 600 !important;
        }

        .filter-option {
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            padding: 8px 0 !important;
            cursor: pointer !important;
            transition: background 0.2s ease !important;
            border-radius: 4px !important;
            padding-left: 8px !important;
            padding-right: 8px !important;
        }

        .filter-option:hover {
            background: #f8f9fa !important;
        }

        .filter-option input[type="checkbox"] {
            margin: 0 !important;
            cursor: pointer !important;
        }

        .filter-option span {
            font-size: 14px !important;
            color: #495057 !important;
        }

        .date-inputs, .amount-inputs {
            display: flex !important;
            flex-direction: column !important;
            gap: 16px !important;
        }

        .date-inputs label, .amount-inputs label {
            display: flex !important;
            flex-direction: column !important;
            gap: 8px !important;
        }

        .date-inputs label span, .amount-inputs label span {
            font-size: 14px !important;
            font-weight: 500 !important;
            color: #495057 !important;
        }

        .date-inputs input, .amount-inputs input {
            padding: 8px 12px !important;
            border: 1px solid #ced4da !important;
            border-radius: 4px !important;
            font-size: 14px !important;
            transition: border-color 0.2s ease !important;
        }

        .date-inputs input:focus, .amount-inputs input:focus {
            outline: none !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25) !important;
        }
        
        .filter-tags-container {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .filter-tag {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 0.85em;
            font-weight: 500;
            border: none;
            box-shadow: 0 2px 4px rgba(0,123,255,0.3);
        }
        
        .filter-tag.status {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            box-shadow: 0 2px 4px rgba(40,167,69,0.3);
        }
        
        .filter-tag.type {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
            box-shadow: 0 2px 4px rgba(23,162,184,0.3);
        }
        
        .filter-tag.date {
            background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
            box-shadow: 0 2px 4px rgba(253,126,20,0.3);
        }
        
        .filter-tag.amount {
            background: linear-gradient(135deg, #6f42c1 0%, #59359a 100%);
            box-shadow: 0 2px 4px rgba(111,66,193,0.3);
        }
        
        .reset-button-container {
            display: flex;
            justify-content: flex-end;
            margin-top: 4px;
        }
        
        .reset-all-button {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(220,53,69,0.3);
            transition: all 0.2s ease;
        }
        
        .reset-all-button:hover {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220,53,69,0.4);
        }
        
        .active-filters-title {
            font-size: 0.9em;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Создаем уникальный ID для панели
        panel_id = f"active-filters-panel-{abs(hash(str(active_filter_tags)))}"

        # Создаем HTML для тегов фильтров
        tags_html = ""
        for tag_data in active_filter_tags:
            if len(tag_data) == 4:  # Новый формат с дополнительными данными
                tag_text, tag_class, filter_type, filter_value = tag_data
                tags_html += f'<span class="filter-tag {tag_class}">{tag_text}</span>'
            else:  # Старый формат для совместимости
                tag_text, tag_class = tag_data
                tags_html += f'<span class="filter-tag {tag_class}">{tag_text}</span>'

        # Создаем скрытую кнопку Streamlit для обработки сброса
        reset_button_key = f"reset_filters_{panel_id}"
        logger.info(f"🔄 ПАНЕЛЬ СБРОСА: Создаем скрытую кнопку сброса с ключом: {reset_button_key}")

        # Создаем скрытую кнопку Streamlit
        reset_clicked = st.button(
            "Reset Filters Hidden",
            key=reset_button_key,
            help="Hidden reset button"
        )

        logger.info(f"🔄 ПАНЕЛЬ СБРОСА: Скрытая кнопка создана, reset_clicked: {reset_clicked}")

        # Отображаем панель активных фильтров с HTML кнопкой внутри
        st.markdown(f"""
        <div id="{panel_id}" class="active-filters-panel">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px; width: 100%;">
                <div style="display: flex; align-items: center; gap: 12px; flex: 1;">
                    <div class="active-filters-title">🔍 Активные фильтры:</div>
                    <div class="filter-tags-container">
                        {tags_html}
                    </div>
                </div>
                <div class="reset-button-container">
                    <button
                        id="reset-filters-btn-{panel_id}"
                        class="reset-all-button"
                        onclick="resetAllFilters()"
                        style="
                            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                            color: white;
                            border: none;
                            border-radius: 6px;
                            padding: 8px 16px;
                            font-size: 14px;
                            font-weight: 500;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            box-shadow: 0 2px 4px rgba(220,53,69,0.2);
                            white-space: nowrap;
                        "
                        onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(220,53,69,0.3)'"
                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(220,53,69,0.2)'"
                    >
                        🗑️ Сбросить все фильтры
                    </button>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # CSS стили и JavaScript для HTML кнопки в панели
        st.markdown(f"""
        <style>
        /* Стили для панели активных фильтров */
        #{panel_id} {{
            position: fixed !important;
            top: 0px !important;
            left: 0px !important;
            right: 0px !important;
            width: 100% !important;
            z-index: 999999 !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-bottom: 2px solid #dee2e6 !important;
            padding: 12px 16px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            margin: 0 !important;
        }}

        /* Скрываем скрытую Streamlit кнопку */
        button[data-testid="baseButton-secondary"][title="Hidden reset button"] {{
            display: none !important;
        }}
        </style>

        <script>
        // Функция сброса всех фильтров
        function resetAllFilters() {{
            console.log('🔄 RESET: HTML кнопка сброса нажата');

            // Находим скрытую Streamlit кнопку и кликаем по ней
            const hiddenButton = document.querySelector('button[data-testid="baseButton-secondary"][title="Hidden reset button"]');
            if (hiddenButton) {{
                console.log('🔄 RESET: Найдена скрытая кнопка, кликаем...');
                hiddenButton.click();
                console.log('🔄 RESET: Скрытая кнопка нажата');
            }} else {{
                console.log('🔄 RESET: ❌ Скрытая кнопка не найдена');

                // Альтернативный способ - ищем любую кнопку с нужным ключом
                const allButtons = document.querySelectorAll('button');
                console.log('🔄 RESET: Ищем среди', allButtons.length, 'кнопок...');

                for (let btn of allButtons) {{
                    const text = btn.textContent;
                    if (text && text.includes('Reset Filters Hidden')) {{
                        console.log('🔄 RESET: Найдена кнопка по тексту, кликаем...');
                        btn.click();
                        return;
                    }}
                }}

                console.log('🔄 RESET: ❌ Кнопка сброса не найдена');
            }}
        }}

        // Применяем стили панели
        (function() {{
            function stylePanel() {{
                const panel = document.getElementById('{panel_id}');
                if (panel) {{
                    panel.style.setProperty('position', 'fixed', 'important');
                    panel.style.setProperty('top', '0px', 'important');
                    panel.style.setProperty('left', '0px', 'important');
                    panel.style.setProperty('right', '0px', 'important');
                    panel.style.setProperty('width', '100%', 'important');
                    panel.style.setProperty('z-index', '999999', 'important');
                    console.log('🔄 PANEL: Стили панели применены');
                }}
            }}

            stylePanel();
            setTimeout(stylePanel, 100);
        }})();
        </script>
        """, unsafe_allow_html=True)

        return reset_clicked
    
    def _get_active_filter_tags(self, filters: Dict[str, Any], available_statuses: List[str],
                               available_types: List[str]) -> List[tuple]:
        """Получает список активных фильтров в виде тегов с интерактивными атрибутами"""
        tags = []

        logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Входные фильтры: {filters}")
        logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Доступные статусы: {available_statuses}")
        logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Доступные типы: {available_types}")

        # Фильтр по статусу
        selected_statuses = filters.get('status', [])
        logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Выбранные статусы: {selected_statuses}")

        if isinstance(selected_statuses, list) and selected_statuses:
            # Показываем тег только если выбраны не все статусы
            if len(selected_statuses) < len(available_statuses):
                status_text = f"Статус: {', '.join(selected_statuses)}"
                tags.append((status_text, "filter-tag-status", "Статус", ', '.join(selected_statuses)))
                logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Добавлен тег статуса: {status_text}")
            else:
                logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Статус не добавлен - выбраны все статусы")

        # Фильтр по типу договора
        selected_types = filters.get('contract_type', [])
        logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Выбранные типы: {selected_types}")

        if isinstance(selected_types, list) and selected_types:
            # Показываем тег только если выбраны не все типы
            if len(selected_types) < len(available_types):
                type_text = f"Тип: {', '.join(selected_types)}"
                tags.append((type_text, "filter-tag-type", "Тип", ', '.join(selected_types)))
                logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Добавлен тег типа: {type_text}")
            else:
                logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Тип не добавлен - выбраны все типы")

        # Фильтр по дате (показываем только если даты действительно установлены)
        start_date = filters.get('start_date')
        end_date = filters.get('end_date')
        if start_date is not None or end_date is not None:
            if start_date and end_date:
                date_text = f"Период: {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}"
                date_value = f"{start_date.strftime('%Y-%m-%d')} - {end_date.strftime('%Y-%m-%d')}"
            elif start_date:
                date_text = f"С даты: {start_date.strftime('%d.%m.%Y')}"
                date_value = f"с {start_date.strftime('%Y-%m-%d')}"
            elif end_date:
                date_text = f"До даты: {end_date.strftime('%d.%m.%Y')}"
                date_value = f"до {end_date.strftime('%Y-%m-%d')}"
            else:
                date_text = None
                date_value = None

            if date_text and date_value:
                tags.append((date_text, "filter-tag-date", "Дата", date_value))

        # Фильтр по сумме (показываем только если установлен не по умолчанию)
        amount_range = filters.get('amount_range')
        if amount_range:
            min_amount, max_amount = amount_range
            # Показываем только если диапазон не является значением по умолчанию (0 - inf)
            if min_amount > 0 or max_amount != float('inf'):
                # Конвертируем в миллионы для отображения
                min_amount_millions = min_amount / 1_000_000
                max_amount_millions = max_amount / 1_000_000 if max_amount != float('inf') else float('inf')

                if max_amount == float('inf'):
                    amount_text = f"Сумма: от {min_amount_millions:,.0f} млн руб"
                    amount_value = f"{min_amount} - inf"
                else:
                    amount_text = f"Сумма: {min_amount_millions:,.0f} - {max_amount_millions:,.0f} млн руб"
                    amount_value = f"{min_amount} - {max_amount}"

                tags.append((amount_text, "filter-tag-amount", "Сумма", amount_value))
                logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: Добавлен тег суммы: {amount_text}")

        logger.info(f"🔍 ТЕГИ ФИЛЬТРОВ: ИТОГО тегов: {len(tags)}")
        return tags
    
    def reset_all_filters(self) -> Dict[str, Any]:
        """Возвращает фильтры по умолчанию"""
        return {
            'status': [],  # Пустой список означает "все статусы"
            'contract_type': [],  # Пустой список означает "все типы"
            'start_date': None,
            'end_date': None,
            'amount_range': (0, float('inf')),  # Диапазон по умолчанию
            'show_charts': True,
            'sort_filter': None,
            'amount_filter': 'Все суммы'  # Добавляем для совместимости
        }

    def apply_main_content_padding(self):
        """Применяет отступ для основного контента, чтобы он не перекрывался фиксированной панелью"""
        st.markdown("""
        <div class="main-content-with-fixed-panel">
        """, unsafe_allow_html=True)

    def close_main_content_padding(self):
        """Закрывает контейнер с отступом для основного контента"""
        st.markdown("""
        </div>
        """, unsafe_allow_html=True)
